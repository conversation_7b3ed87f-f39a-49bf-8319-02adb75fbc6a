from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh,write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
from pandas import json_normalize
import json
import numpy as np
import pendulum
dag_name = 'DAILY_W_APP_MY88_USERREGISTER_D'
description = 'Get data CALL DETAIL FROM APP MY88 TO DWH'
notify_conn_id = 'f88app'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.APP_MY88_USER_REGISTER'
proc_name = 'F88DWH.PROC_W_APP_MY88_USER_REGIS_D'
tags = ['app_my88','daily']
schedule_interval = '15 7 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
TODAY_PROC = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
YESTERDAY_PROC = TODAY_PROC - timedelta(days=1)
# test connect

def query(**context):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')
    logical_date_tz = context['logical_date'].astimezone(tz)
    logical_date = context['logical_date']  # kiểu datetime.datetime
    if logical_date_tz.date() == pendulum.now(tz=tz).date():
        target_date = logical_date - timedelta(days=1).date()
    else:
        target_date = logical_date
    # Dải thời gian từ 00:00:00 đến 23:59:59.999 (UTC)
    start_time = datetime.combine(target_date, datetime.min.time())
    end_time = datetime.combine(target_date, datetime.max.time()).replace(microsecond=999000)
    mongo_query = {
        "$or": [
            {
                "CreatedDate": {
                    "$gte": start_time,
                    "$lt": end_time
                }
            },
            {
                "ModifiedDate": {
                    "$gte": start_time,
                    "$lt": end_time
                }
            }
        ]
    }
    logging.info("=== MongoDB Query Builder ===")
    logging.info(f"Airflow logical_date (UTC): {logical_date.isoformat()}Z")
    logging.info(f"Target date for Mongo query: {target_date}")
    logging.info(f"Query FROM: {start_time.isoformat()}Z")
    logging.info(f"Query TO  : {end_time.isoformat()}Z")
    logging.info(f"Mongo Query: {mongo_query}")


    # Chuyển đổi truy vấn thành chuỗi JSON cho hook
    return mongo_query
def custom_app_my88(df):
    data = df
    data = data.rename(columns=str.lower)  # Đổi tất cả tên cột thành chữ thường
    datetime_columns = [
            'birthday', 'identitydate', 'lastchangepasswordat', 'activeat',
            'create_date', 'modify_date', 'createddate', 'modifieddate'
        ]
    # Duyệt qua từng cột ngày giờ và xử lý
    timestamp_value = pd.Timestamp('1970-01-01')
    for col in datetime_columns:
        if col in data.columns:
            # Kiểm tra nếu cột không phải kiểu datetime
            if not pd.api.types.is_datetime64_any_dtype(data[col]):
                # Chuyển đổi nếu cột không phải kiểu datetime
                data[col] = pd.to_datetime(data[col], errors='coerce')

            # Thay NaT bằng ngày mặc định (1970-01-01)
            data[col] = data[col].fillna(timestamp_value)
            data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')

            # Kiểm tra kiểu dữ liệu sau khi thay thế
            logging.info(f'Kiểu dữ liệu của cột {col} sau khi thay thế NaT: {data[col].dtype}')
            # In ra kết quả để kiểm tra

    # Kiểm tra và xử lý cột 'setting' (chuyển thành chuỗi JSON nếu là dictionary)
    if 'setting' in data.columns:
        data['setting'] = data['setting'].apply(lambda x: json.dumps(x) if isinstance(x, dict) else x)
        settings_df = json_normalize(data['setting'].apply(json.loads))  # Normalize JSON
        data = pd.concat([data, settings_df], axis=1)
    if 'NFCRawData' in data.columns:
        data['NFCRawData'] = data['NFCRawData'].apply(lambda x: json.dumps(x) if isinstance(x, dict) else x)
    if 'requestdata' in data.columns:
        try:
            # Giải mã chuỗi JSON trong cột 'requestdata' và lấy key 'Data'
            request_data_json = data['requestdata'].apply(lambda x: json.loads(x) if isinstance(x, str) else {})

            # Kiểm tra xem có 'Data' trong nội dung không và chuẩn hóa dữ liệu từ key 'Data'
            request_data_json = request_data_json.apply(lambda x: x.get('Data', {}) if isinstance(x, dict) else {})

            # Chỉ lấy các cột cần thiết từ 'RequestData' (RequestID và MerchantCode)
            request_data_normalized = json_normalize(request_data_json)

            # Chỉ giữ lại những cột cần thiết: 'RequestId' và 'MerchantCode'
            request_data_normalized = request_data_normalized[['RequestId', 'MerchantCode']]

            # Nối các cột mới vào DataFrame gốc
            data = pd.concat([data, request_data_normalized], axis=1)
        except Exception as e:
            print(f"Lỗi khi xử lý RequestData: {e}")

    if 'responsedata' in data.columns:
        try:
            # Giải mã chuỗi JSON trong cột 'responsedata' và lấy key 'Content'
            response_data_json = data['responsedata'].apply(lambda x: json.loads(x) if isinstance(x, str) else {})

            # Kiểm tra xem có 'Content' trong nội dung không và chuẩn hóa dữ liệu từ key 'Content'
            response_data_json = response_data_json.apply(
                lambda x: x.get('Content', {}) if isinstance(x, dict) else {})

            # Chỉ lấy cột 'Status' từ 'ResponseData'
            response_data_normalized = json_normalize(response_data_json)

            # Chỉ giữ lại cột 'Status'
            response_data_normalized = response_data_normalized[['Status']]

            # Nối các cột mới vào DataFrame gốc
            data = pd.concat([data, response_data_normalized], axis=1)
        except Exception as e:
            print(f"Lỗi khi xử lý ResponseData: {e}")
    return data

def ingestion(source_conn_id,target_conn_id, stg_tbl_name,  **context):
    # init value sql and fill param for quert
    # Get data from source

    try:
        hook =MongoHook(conn_id=source_conn_id)
        client = hook.get_conn()
        logging.info("connect mongodb susscess")
    except Exception as e:
        logging.error(str(e))
    try:
        try:
            db = client.f88app
            collection = db.UserRegister
        except Exception as e:
            print(str(e))
        # Thực hiện truy vấn find trên Collection
        results = collection.find(query(**context))
        logging.info("results susscess")
    except Exception as e:
        logging.error(str(e))
    try:
        notify_df = pd.DataFrame(list(results))
        logging.info("dataframe susscess")
    except Exception as e:
        logging.error(str(e))
    print(notify_df.dtypes)
    notify_df = notify_df.fillna('')
    # Assuming your DataFrame is named 'autocall_df'
    notify_df.rename(columns={'type': 'type_name', '_id': 'id'}, inplace=True)
    # Chuyển đổi ObjectId thành chuỗi nếu có
    for col in notify_df.columns:
        if notify_df[col].dtype == 'object':  # Nếu là kiểu object (bao gồm ObjectId)
            notify_df[col] = notify_df[col].apply(lambda x: str(x) if isinstance(x, ObjectId) else x)
            # Thay thế NaN bằng chuỗi rỗng
            notify_df[col] = notify_df[col].fillna("")
        elif np.issubdtype(notify_df[col].dtype, np.number):  # Cột kiểu số
            # Thay thế NaN bằng 0 cho cột số
            notify_df[col] = notify_df[col].fillna(0)
    # ingest data to staging layer of dwh
    write_df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=custom_app_my88(notify_df))
    logging.info('Get data source to stg done!')

def merge_transform(conn_id, proc_name, **context):
    today =int(YESTERDAY_PROC.strftime('%Y%m%d'))
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)
with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                        provide_context=True)


    ingestion_app_my88_UserRegister = PythonOperator(
        task_id='ingestion_app_my88_UserRegister',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': notify_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )


    merge_data_stag_to_dwh_app_my88_UserRegister = PythonOperator(
        task_id='merge_data_stag_to_dwh_app_my88_UserRegister',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )
    #
#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table >> ingestion_app_my88_UserRegister >> merge_data_stag_to_dwh_app_my88_UserRegister
