from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from datetime import timedelta, datetime

# G<PERSON><PERSON> các hàm bạn đã định nghĩa
from DAGs.Datamart_SendMail.SendMail import send_email_to_datamart

default_args = {
    'owner': 'qtdl',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=2),
}

# Định nghĩa DAG
with DAG(
    'dag_datamart_daily_email',
    default_args=default_args,
    description='DAG gửi email thống kê so sánh giữa DWH và Data Mart hàng ngày',
    schedule_interval='45 9 * * *',  # chạy lúc 7h sáng hàng ngày
    start_date=datetime(2024, 6, 1),
    catchup=False,
    tags=['datamart', 'email', 'monitor'],
) as dag:

    def run_send_email():
        # Bạn có thể cấu hình lại 3 tham số này tùy vào mục đích
        print("start send email")
        send_email_to_datamart(
            requestor=['qtdl', 'kienpv','cskd.bdm','nhily2','luongct','hangbtt2'],       # username email
            status=1,               # status = 1 (success), 0 (fail)
            reason='Daily check',   # lý do (nếu cần log)
            dagId='dag_datamart_daily_email'
        )

    send_email_task = PythonOperator(
        task_id='send_datamart_email',
        python_callable=run_send_email
    )

    send_email_task
