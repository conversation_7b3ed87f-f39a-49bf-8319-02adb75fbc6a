# DATA INTEGRATION V3 - AIRFLOW REPOSITORY
Dự án này nhằm mục đích lập lịch, phát triển xây dựng các tác vụ nhằm mục đích kéo dữ liệu từ các nguồn (SharePoint, S3, Gapo, ...) về Datawarehouse và ngược lại từ Datawarehouse ra các báo cáo ở các nguồn khác (SharePoint, S3, Gapo, ...)

### 1. <PERSON><PERSON><PERSON> trúc thư mục
Về cấu trúc sẽ có các thư mục chính 
- DAGs : <PERSON><PERSON><PERSON> trữ các DAG (AIRFLOW)
- MONITOR: Source code của UI để thực hiện thêm sửa xóa các DAGs (FLASK + HTML/BOOSTRAP/JAVASCRIPTS)
- Provider: Chứa custom provider (function, hooks, operators).
- SendMail: Function gửi mail.
- requirements.txt
```bash
.airflowignore
.gitignore
DAGs
   |-- BOT_GAPO
   |-- DAILY_REPORT_ODI_JOBS.py
   |-- DWH2S3
   |   |-- DAILY
   |   |   |-- MAIN.py
   |-- DWH2SharePoint
   |   |-- DAILY
   |   |   |-- CIMB
   |   |   |-- MAIN.py
   |   |   |-- NGOAILE
   |   |   |-- QLV
   |   |-- MONTHLY
   |   |   |-- KT
   |   |   |-- MAIN.py
   |   |   |-- PTKD
   |   |-- REALTIME
   |   |   |-- TDTD
   |   |   |-- VH
   |   |   |-- XLN
   |   |-- WEEKLY
   |   |   |-- MAIN.py
   |   |   |-- PTKD
   |   |   |-- XLN
   |-- External2DWH
   |   |-- API
   |   |   |-- BaseAPI
   |   |   |   |-- HR
   |   |   |   |   |-- DAILY
   |   |   |   |   |-- MONTHLY
   |   |   |   |   |-- utils.py
   |   |-- AWS_DOCUMENTDB
   |   |-- AWS_SQS
   |   |-- SharePoint
   |   |   |-- KPI
   |   |   |-- MAIN.py
   |   |   |-- NGOAILE
   |-- utils.py
MONITOR
Provider
   |-- COMMON
   |-- GoogleSheetProvider
   |-- OracleProvider
   |   |-- hooks
   |   |   |-- DDLHooks.py
   |   |-- operators
   |   |   |-- DDLOperators.py
   |   |   |-- FILE2DWHOperators.py
   |   |   |-- GetDataOperators.py
   |   |   |-- STG2DWHOperators.py
   |   |-- utils.py
   |-- RedShiftProvider
   |-- S3Provider
   |   |-- hooks
   |   |   |-- UploadHook.py
   |   |-- operators
   |   |   |-- UploadOperators.py
   |-- SharePointProvider
   |   |-- hooks
   |   |   |-- DeleteHook.py
   |   |   |-- DownloadHook.py
   |   |   |-- UploadHooks.py
   |   |-- operators
   |   |   |-- DownloadOperators.py
   |   |   |-- UploadOperators.py
   |   |-- xlsx_to_dwh_utils
   |   |   |-- utils.py
   |-- utils.py
README.md
SendMail
   |-- SendMail.py
```

### 2. Phân loại pipeline
Sẽ có 3 loại pipeline chính là 
- DWH2S3: Lấy dữ liệu từ DWH (bằng truy vấn sql) sau đó đẩy dữ liệu lên S3 (Amazon Web Service S3)
- DWH2SHAREPOINT: Lấy dữ liệu từ DWH (bằng truy vấn sql) và sau đó đẩy báo cáo (định dạng xlsx) lên folder SharePoint.
- External2DWH: Thực hiện kéo dữ liệu từ các nguồn như (API, Sharepoint) về DWH để phục vụ phân tích và xây dựng báo cáo BI.

### Dependency
- Python Version >= 3.10
- requirements.txt
- xlsx-to-csv lib: https://github.com/hmarr/xlsx-to-csv/tree/main